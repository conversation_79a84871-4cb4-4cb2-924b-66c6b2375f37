import { ProgrammingLanguage } from '../types/solutions'

// Map our language types to react-syntax-highlighter language identifiers
export function mapLanguageToHighlighter(language: ProgrammingLanguage): string {
  const languageMap: Record<ProgrammingLanguage, string> = {
    'python': 'python',
    'javascript': 'javascript',
    'typescript': 'typescript',
    'java': 'java',
    'cpp': 'cpp',
    'c': 'c',
    'csharp': 'csharp',
    'sql': 'sql',
    'bash': 'bash',
    'shell': 'bash',
    'go': 'go',
    'rust': 'rust',
    'php': 'php',
    'ruby': 'ruby',
    'swift': 'swift',
    'kotlin': 'kotlin',
    'html': 'html',
    'css': 'css',
    'json': 'json',
    'yaml': 'yaml',
    'xml': 'xml',
    'markdown': 'markdown',
    'text': 'text'
  }

  return languageMap[language] || 'python' // Default to python if not found
}

// Get language display name for UI
export function getLanguageDisplayName(language: ProgrammingLanguage): string {
  const displayNames: Record<ProgrammingLanguage, string> = {
    'python': 'Python',
    'javascript': 'JavaScript',
    'typescript': 'TypeScript',
    'java': 'Java',
    'cpp': 'C++',
    'c': 'C',
    'csharp': 'C#',
    'sql': 'SQL',
    'bash': 'Bash',
    'shell': 'Shell',
    'go': 'Go',
    'rust': 'Rust',
    'php': 'PHP',
    'ruby': 'Ruby',
    'swift': 'Swift',
    'kotlin': 'Kotlin',
    'html': 'HTML',
    'css': 'CSS',
    'json': 'JSON',
    'yaml': 'YAML',
    'xml': 'XML',
    'markdown': 'Markdown',
    'text': 'Text'
  }

  return displayNames[language] || 'Python'
}

// Detect language from code content (client-side fallback)
export function detectLanguageFromCode(code: string): ProgrammingLanguage {
  if (!code || typeof code !== 'string') {
    return 'python'
  }

  const content = code.toLowerCase()

  // SQL detection
  if (/\b(select|from|where|insert|update|delete|create|table|database|join|group by|order by)\b/gi.test(content)) {
    return 'sql'
  }

  // Bash/Shell detection
  if (/^#!/.test(code) || /\$\w+/.test(code) || /\becho\b|\bls\b|\bcd\b|\bmkdir\b/.test(content)) {
    return 'bash'
  }

  // JavaScript detection
  if (/\bfunction\s+\w+\s*\(|\bconst\s+\w+|\bconsole\.log\s*\(|\b(async|await)\b/gi.test(content)) {
    return content.includes('interface ') || content.includes(': string') ? 'typescript' : 'javascript'
  }

  // Java detection
  if (/\bpublic\s+class\s+\w+|\bpublic\s+static\s+void\s+main|\bSystem\.out\.print/gi.test(content)) {
    return 'java'
  }

  // C/C++ detection
  if (/#include\s*<.*>|\bint\s+main\s*\(|\bstd::|printf\s*\(/gi.test(content)) {
    return content.includes('std::') || content.includes('cout') ? 'cpp' : 'c'
  }

  // Python detection (default but with some indicators)
  if (/\bdef\s+\w+\s*\(|\bclass\s+\w+|\bimport\s+\w+|\bprint\s*\(/gi.test(content)) {
    return 'python'
  }

  // Default to Python
  return 'python'
}
