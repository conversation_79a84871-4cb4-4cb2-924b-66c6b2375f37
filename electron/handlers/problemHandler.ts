// Import necessary modules
import axios from "axios"
import { store } from "../store"

// Define types for language detection
type ProgrammingLanguage =
  | 'python'
  | 'javascript'
  | 'typescript'
  | 'java'
  | 'cpp'
  | 'c'
  | 'csharp'
  | 'sql'
  | 'bash'
  | 'shell'
  | 'go'
  | 'rust'
  | 'php'
  | 'ruby'
  | 'swift'
  | 'kotlin'
  | 'html'
  | 'css'
  | 'json'
  | 'yaml'
  | 'xml'
  | 'markdown'
  | 'text'

interface LanguageDetectionResult {
  language: ProgrammingLanguage
  confidence: number
  indicators: string[]
}

// Define interfaces for ProblemInfo and related structures

interface DebugSolutionResponse {
  thoughts: string[]
  old_code: string
  new_code: string
  time_complexity: string
  space_complexity: string
  language?: ProgrammingLanguage
}

interface ProblemInfo {
  problem_type: "MCQ" | "CODING"
  problem_statement?: string
  detected_language?: ProgrammingLanguage
  language_confidence?: number
  input_format?: {
    description?: string
    parameters?: Array<{
      name: string
      type: string
      subtype?: string
    }>
  }
  output_format?: {
    description?: string
    type?: string
    subtype?: string
  }
  constraints?: Array<{
    description: string
    parameter?: string
    range?: {
      min?: number
      max?: number
    }
  }>
  test_cases?: any // Adjust the type as needed
  choices?: string[]
  is_multi_select?: boolean
}

export async function getApiKey(): Promise<string | null> {
  return store.get("openaiApiKey")
}

// Language detection utility function
function detectLanguageFromContent(content: string): LanguageDetectionResult {
  const indicators: string[] = []
  let language: ProgrammingLanguage = 'python' // Default
  let confidence = 0.3 // Default low confidence

  // SQL indicators
  const sqlKeywords = /\b(SELECT|FROM|WHERE|INSERT|UPDATE|DELETE|CREATE|TABLE|DATABASE|JOIN|GROUP BY|ORDER BY|HAVING|UNION|ALTER|DROP)\b/gi
  if (sqlKeywords.test(content)) {
    indicators.push('SQL keywords detected')
    language = 'sql'
    confidence = 0.9
  }

  // Bash/Shell indicators
  const bashIndicators = [
    /^#!/, // Shebang
    /\$\w+/, // Variables like $HOME, $PATH
    /\|\s*\w+/, // Pipes
    /&&|\|\|/, // Logical operators
    /\becho\b|\bls\b|\bcd\b|\bmkdir\b|\brm\b|\bcp\b|\bmv\b|\bgrep\b|\bawk\b|\bsed\b/gi, // Common commands
    /\bif\s+\[.*\];\s*then\b/gi, // Bash if statements
    /\bfor\s+\w+\s+in\b/gi // Bash for loops
  ]

  let bashMatches = 0
  bashIndicators.forEach(pattern => {
    if (pattern.test(content)) {
      bashMatches++
      indicators.push('Bash/Shell syntax detected')
    }
  })

  if (bashMatches >= 2) {
    language = 'bash'
    confidence = 0.85
  }

  // Python indicators (higher priority than default)
  const pythonIndicators = [
    /\bdef\s+\w+\s*\(/gi, // Function definitions
    /\bclass\s+\w+/gi, // Class definitions
    /\bimport\s+\w+|\bfrom\s+\w+\s+import\b/gi, // Import statements
    /\bif\s+__name__\s*==\s*['"']__main__['"']/gi, // Main guard
    /\bprint\s*\(/gi, // Print function
    /\brange\s*\(/gi, // Range function
    /\blen\s*\(/gi, // Len function
    /:\s*$/, // Colon at end of line (indentation-based)
    /^\s{4}|\t/gm // Indentation patterns
  ]

  let pythonMatches = 0
  pythonIndicators.forEach(pattern => {
    if (pattern.test(content)) {
      pythonMatches++
      indicators.push('Python syntax detected')
    }
  })

  if (pythonMatches >= 2) {
    language = 'python'
    confidence = Math.min(0.9, 0.5 + (pythonMatches * 0.1))
  }

  // JavaScript/TypeScript indicators
  const jsIndicators = [
    /\bfunction\s+\w+\s*\(/gi,
    /\bconst\s+\w+|\blet\s+\w+|\bvar\s+\w+/gi,
    /\bconsole\.log\s*\(/gi,
    /\b(async|await)\b/gi,
    /=>\s*{?/gi, // Arrow functions
    /\brequire\s*\(|\bimport\s+.*from\b/gi
  ]

  let jsMatches = 0
  jsIndicators.forEach(pattern => {
    if (pattern.test(content)) {
      jsMatches++
      indicators.push('JavaScript syntax detected')
    }
  })

  if (jsMatches >= 2) {
    language = content.includes('interface ') || content.includes(': string') || content.includes(': number')
      ? 'typescript' : 'javascript'
    confidence = 0.8
  }

  // Java indicators
  const javaIndicators = [
    /\bpublic\s+class\s+\w+/gi,
    /\bpublic\s+static\s+void\s+main/gi,
    /\bSystem\.out\.print/gi,
    /\bpublic\s+\w+\s+\w+\s*\(/gi,
    /\bprivate\s+\w+\s+\w+/gi
  ]

  let javaMatches = 0
  javaIndicators.forEach(pattern => {
    if (pattern.test(content)) {
      javaMatches++
      indicators.push('Java syntax detected')
    }
  })

  if (javaMatches >= 2) {
    language = 'java'
    confidence = 0.85
  }

  // C/C++ indicators
  const cppIndicators = [
    /#include\s*<.*>/gi,
    /\bint\s+main\s*\(/gi,
    /\bstd::/gi,
    /\bcout\s*<<|\bcin\s*>>/gi,
    /\bprintf\s*\(|\bscanf\s*\(/gi
  ]

  let cppMatches = 0
  cppIndicators.forEach(pattern => {
    if (pattern.test(content)) {
      cppMatches++
      indicators.push('C/C++ syntax detected')
    }
  })

  if (cppMatches >= 2) {
    language = content.includes('std::') || content.includes('cout') ? 'cpp' : 'c'
    confidence = 0.85
  }

  return { language, confidence, indicators }
}

// Language-specific instruction helper
function getLanguageSpecificInstructions(language: ProgrammingLanguage) {
  switch (language) {
    case 'sql':
      return {
        contextPrompt: "This appears to be a SQL database query problem. Focus on proper SQL syntax, query optimization, and database best practices.",
        codeDescription: "The SQL solution with comments explaining the query logic",
        complexityNote: "Query complexity in terms of table scans and joins",
        additionalRequirements: "\n6. Use proper SQL formatting with keywords in UPPERCASE\n7. Include appropriate JOINs, WHERE clauses, and aggregations as needed\n8. Consider indexing implications in complexity analysis"
      }

    case 'bash':
    case 'shell':
      return {
        contextPrompt: "This appears to be a shell scripting problem. Focus on proper bash syntax, command chaining, and shell best practices.",
        codeDescription: "The bash/shell script solution with comments explaining each command",
        complexityNote: "Script complexity in terms of command execution and file operations",
        additionalRequirements: "\n6. Include proper shebang (#!/bin/bash) if needed\n7. Use proper variable syntax ($VAR or ${VAR})\n8. Include error handling where appropriate\n9. Consider portability across different shell environments"
      }

    case 'javascript':
      return {
        contextPrompt: "This appears to be a JavaScript programming problem. Focus on modern ES6+ syntax, proper variable declarations, and JavaScript best practices.",
        codeDescription: "The JavaScript solution with comments explaining the code logic",
        complexityNote: "Time/space complexity in Big O notation",
        additionalRequirements: "\n6. Use const/let instead of var\n7. Consider using arrow functions where appropriate\n8. Include proper error handling\n9. Use modern JavaScript features when beneficial"
      }

    case 'java':
      return {
        contextPrompt: "This appears to be a Java programming problem. Focus on proper object-oriented design, Java conventions, and performance considerations.",
        codeDescription: "The Java solution with comments explaining the code logic",
        complexityNote: "Time/space complexity in Big O notation",
        additionalRequirements: "\n6. Follow Java naming conventions (camelCase for methods/variables, PascalCase for classes)\n7. Include proper access modifiers\n8. Consider using appropriate data structures from Java Collections Framework\n9. Include proper exception handling where needed"
      }

    case 'cpp':
    case 'c':
      return {
        contextPrompt: `This appears to be a ${language.toUpperCase()} programming problem. Focus on memory management, performance optimization, and proper ${language.toUpperCase()} syntax.`,
        codeDescription: `The ${language.toUpperCase()} solution with comments explaining the code logic`,
        complexityNote: "Time/space complexity in Big O notation",
        additionalRequirements: `\n6. Include proper header files\n7. Use appropriate data types and memory management\n8. Consider pointer usage and memory allocation\n9. Include proper error checking for system calls`
      }

    case 'python':
    default:
      return {
        contextPrompt: "This appears to be a Python programming problem. Focus on Pythonic solutions, proper data structures, and clean, readable code.",
        codeDescription: "The Python solution with comments explaining the code logic",
        complexityNote: "Time/space complexity in Big O notation",
        additionalRequirements: "\n6. Follow PEP 8 style guidelines\n7. Use appropriate Python data structures (list, dict, set, etc.)\n8. Consider using built-in functions and libraries when appropriate\n9. Include proper error handling where needed"
      }
  }
}

// Define the extractProblemInfo function
export async function extractProblemInfo(
  imageDataList: string[]
): Promise<any> {
  const storedApiKey = store.get("openaiApiKey")
  if (!storedApiKey) {
    throw new Error("OpenAI API key not set")
  }

  // Prepare the image contents for the message
  const imageContents = imageDataList.map((imageData) => ({
    type: "image_url",
    image_url: {
      url: `data:image/jpeg;base64,${imageData}`
    }
  }))

  // Construct the messages to send to the model
  const messages = [
    {
      role: "user",
      content: [
        {
          type: "text",
          text:
            "First determine if this is a Multiple Choice Question (MCQ) or a Coding Problem.\n" +
            "Look for indicators like:\n" +
            "- lettered/numbered options (A,B,C,D or 1,2,3,4)\n" +
            "- 'true/false' choices\n" +
            "- 'select all that apply' instructions\n" +
            "\n" +
            "If it's a Coding Problem, also identify the programming language by looking for:\n" +
            "- SQL: Keywords like SELECT, FROM, WHERE, INSERT, UPDATE, CREATE TABLE, JOIN, etc.\n" +
            "- Bash/Shell: Commands like echo, ls, cd, grep, awk, sed, pipes (|), variables ($VAR), shebang (#!)\n" +
            "- Python: def, class, import, print(), range(), len(), indentation-based syntax\n" +
            "- JavaScript: function, const/let/var, console.log(), arrow functions (=>), require/import\n" +
            "- Java: public class, public static void main, System.out.print\n" +
            "- C/C++: #include, int main(), std::, cout/cin, printf/scanf\n" +
            "- Default to Python if language cannot be determined clearly\n" +
            "\n" +
            "Then extract the following information based on the type:\n" +
            "For MCQ:\n" +
            "1. Complete problem statement, including any background information and question text. If a figure (eg table, combined chart, line graph, bar graph, piechart etc) is present, extract all data relevant to answer the question text.\n" +
            "2. Available choices\n" +
            "3. Any relevant context\n" +
            "For Coding Problems:\n" +
            "1. Problem statement\n" +
            "2. Input/Output format\n" +
            "3. Constraints\n" +
            "4. Example test cases\n"
        },
        ...imageContents
      ]
    }
  ]

  // Define the function schema
  const functions = [
    {
      name: "extract_problem_details",
      description:
        "Extract and structure the key components of a coding problem",
      parameters: {
        type: "object",
        properties: {
          problem_type: {
            type: "string",
            enum: ["MCQ", "CODING"],
            description: "Type of the problem"
          },
          problem_statement: {
            type: "string",
            description:
              "The ENTIRE main problem statement describing what needs to be solved"
          },
          detected_language: {
            type: "string",
            enum: ["python", "javascript", "typescript", "java", "cpp", "c", "csharp", "sql", "bash", "shell", "go", "rust", "php", "ruby", "swift", "kotlin", "html", "css", "json", "yaml", "xml", "markdown", "text"],
            description: "The programming language detected from the code in the image. Default to 'python' if unclear."
          },
          language_confidence: {
            type: "number",
            minimum: 0,
            maximum: 1,
            description: "Confidence level (0-1) in the language detection"
          },
          input_format: {
            type: "object",
            properties: {
              description: {
                type: "string",
                description: "Description of the input format"
              },
              parameters: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    name: {
                      type: "string",
                      description: "Name of the parameter"
                    },
                    type: {
                      type: "string",
                      enum: [
                        "number",
                        "string",
                        "array",
                        "array2d",
                        "array3d",
                        "matrix",
                        "tree",
                        "graph"
                      ],
                      description: "Type of the parameter"
                    },
                    subtype: {
                      type: "string",
                      enum: ["integer", "float", "string", "char", "boolean"],
                      description: "For arrays, specifies the type of elements"
                    }
                  },
                  required: ["name", "type"]
                }
              }
            },
            required: ["description", "parameters"]
          },
          output_format: {
            type: "object",
            properties: {
              description: {
                type: "string",
                description: "Description of the expected output format"
              },
              type: {
                type: "string",
                enum: [
                  "number",
                  "string",
                  "array",
                  "array2d",
                  "array3d",
                  "matrix",
                  "boolean"
                ],
                description: "Type of the output"
              },
              subtype: {
                type: "string",
                enum: ["integer", "float", "string", "char", "boolean"],
                description: "For arrays, specifies the type of elements"
              }
            },
            required: ["description", "type"]
          },
          constraints: {
            type: "array",
            items: {
              type: "object",
              properties: {
                description: {
                  type: "string",
                  description: "Description of the constraint"
                },
                parameter: {
                  type: "string",
                  description: "The parameter this constraint applies to"
                },
                range: {
                  type: "object",
                  properties: {
                    min: { type: "number" },
                    max: { type: "number" }
                  }
                }
              },
              required: ["description"]
            }
          },
          test_cases: {
            type: "array",
            items: {
              type: "object",
              properties: {
                input: {
                  type: "object",
                  properties: {
                    args: {
                      type: "array",
                      items: {
                        anyOf: [
                          { type: "integer" },
                          { type: "string" },
                          {
                            type: "array",
                            items: {
                              anyOf: [
                                { type: "integer" },
                                { type: "string" },
                                { type: "boolean" },
                                { type: "null" }
                              ]
                            }
                          },
                          { type: "object" },
                          { type: "boolean" },
                          { type: "null" }
                        ]
                      }
                    }
                  },
                  required: ["args"]
                },
                output: {
                  type: "object",
                  properties: {
                    result: {
                      anyOf: [
                        { type: "integer" },
                        { type: "string" },
                        {
                          type: "array",
                          items: {
                            anyOf: [
                              { type: "integer" },
                              { type: "string" },
                              { type: "boolean" },
                              { type: "null" }
                            ]
                          }
                        },
                        { type: "object" },
                        { type: "boolean" },
                        { type: "null" }
                      ]
                    }
                  },
                  required: ["result"]
                }
              },
              required: ["input", "output"]
            },
            minItems: 1
          },
          choices: {
            type: "array",
            items: {
              type: "string"
            },
            description: "Available choices for MCQ"
          },
          is_multi_select: {
            type: "boolean",
            description: "Whether multiple answers can be selected"
          }
        },
        required: ["problem_type", "problem_statement", "detected_language", "language_confidence"]
      }
    }
  ]

  // Prepare the request payload
  const payload = {
    model: "gpt-4o-mini",
    messages: messages,
    functions: functions,
    function_call: { name: "extract_problem_details" },
    max_tokens: 4096
  }

  try {
    // Send the request to the completion endpoint
    const response = await axios.post(
      "https://api.openai.com/v1/chat/completions",
      payload,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${storedApiKey}`
        }
      }
    )

    // Extract the function call arguments from the response
    const functionCallArguments =
      response.data.choices[0].message.function_call.arguments

    // Return the parsed function call arguments
    return JSON.parse(functionCallArguments)
  } catch (error) {
    if (error.response?.status === 401) {
      throw new Error(
        "Please close this window and re-enter a valid Open AI API key."
      )
    }
    if (error.response?.status === 429) {
      throw new Error(
        "API Key out of credits. Please refill your OpenAI API credits and try again."
      )
    }

    throw error
  }
}

export async function generateSolutionResponses(
  problemInfo: ProblemInfo
): Promise<any> {
  try {
    const storedApiKey = store.get("openaiApiKey") as string
    if (!storedApiKey) {
      throw new Error("OpenAI API key not set")
    }

    if (problemInfo.problem_type === "MCQ") {
      const mcqPrompt = `Given this ${problemInfo.is_multi_select ? 'multi-select' : 'single-select'} multiple choice question:

Question: ${problemInfo.problem_statement}

Choices:
${problemInfo.choices?.map((choice, idx) => `${idx + 1}. ${choice}`).join('\n')}

Please analyze the question and choices carefully, then:
1. ${problemInfo.is_multi_select ? 'Identify ALL correct answers' : 'Identify the correct answer'}
2. Explain your reasoning briefly

Response should be in this format:
{
  "thoughts": [
    "Initial analysis of the question",
    "Key consideration that points to the answer(s)",
    "Final confirmation of choice(s)"
  ],
  "code": "${problemInfo.is_multi_select ? 
    'ALL correct answers with numbers, format: "2,4. Second and fourth answers are correct because..."' : 
    'The answer number and text, format: "2. Correct answer text"'
  }",
  "time_complexity": "Not applicable for MCQ",
  "space_complexity": "Not applicable for MCQ"
}`

      const response = await axios.post(
        "https://api.openai.com/v1/chat/completions",
        {
          model: "gpt-4o-mini",
          messages: [
            {
              role: "user",
              content: mcqPrompt
            }
          ],
          temperature: 0 // Add this to get more consistent results
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${storedApiKey}`
          }
        }
      )

      const content = response.data.choices[0].message.content
      return JSON.parse(content)
    }

    // Handle coding problems
    const detectedLanguage = problemInfo.detected_language || 'python'
    const languageInstructions = getLanguageSpecificInstructions(detectedLanguage)

    const promptContent = `Given the following coding problem:

Problem Statement:
${problemInfo.problem_statement ?? "Problem statement not available"}

Detected Programming Language: ${detectedLanguage.toUpperCase()}
Language Confidence: ${problemInfo.language_confidence || 0.5}

${languageInstructions.contextPrompt}

Input Format:
${problemInfo.input_format?.description ?? "Input format not available"}
Parameters:
${
  problemInfo.input_format?.parameters
    ?.map((p) => `- ${p.name}: ${p.type}${p.subtype ? ` of ${p.subtype}` : ""}`)
    .join("\n") ?? "No parameters available"
}

Output Format:
${problemInfo.output_format?.description ?? "Output format not available"}
Returns: ${problemInfo.output_format?.type ?? "Type not specified"}${
      problemInfo.output_format?.subtype
        ? ` of ${problemInfo.output_format.subtype}`
        : ""
    }

Constraints:
${
  problemInfo.constraints
    ?.map((c) => {
      let constraintStr = `- ${c.description}`
      if (c.range) {
        constraintStr += ` (${c.parameter}: ${c.range.min} to ${c.range.max})`
      }
      return constraintStr
    })
    .join("\n") ?? "No constraints specified"
}

Test Cases:
${JSON.stringify(problemInfo.test_cases ?? "No test cases available", null, 2)}

Generate a solution in this format:
{
  "thoughts": [
    "First thought showing recognition of the problem and core challenge",
    "Second thought naming specific algorithm/data structure being considered",
    "Third thought showing confidence in approach while acknowledging details needed"
  ],
  "code": "${languageInstructions.codeDescription}",
  "time_complexity": "${languageInstructions.complexityNote}",
  "space_complexity": "${languageInstructions.complexityNote}"
}

Format Requirements:
1. Use actual line breaks in code field
2. Indent code properly with spaces or tabs as appropriate for ${detectedLanguage}
3. Include clear code comments in ${detectedLanguage} style
4. Response must be valid JSON
5. Return only the JSON object with no markdown or other formatting
${languageInstructions.additionalRequirements}`

    const response = await axios.post(
      "https://api.openai.com/v1/chat/completions",
      {
        model: "gpt-4o-mini",
        messages: [
          {
            role: "user",
            content: promptContent
          }
        ]
      },
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${storedApiKey}`
        }
      }
    )

    const content = response.data.choices[0].message.content
    return JSON.parse(content)
  } catch (error: any) {
    if (error.response?.status === 401) {
      throw new Error(
        "Please close this window and re-enter a valid Open AI API key."
      )
    }
    if (error.response?.status === 429) {
      throw new Error(
        "API Key out of credits. Please refill your OpenAI API credits and try again."
      )
    }
    console.error("Error details:", error)
    throw new Error(`Error generating solutions: ${error.message}`)
  }
}

export async function debugSolutionResponses(
  imageDataList: string[],
  problemInfo: ProblemInfo
): Promise<DebugSolutionResponse> {
  if (problemInfo.problem_type === "MCQ") {
    return {
      thoughts: ["MCQ solutions don't require debugging"],
      old_code: "Not applicable for MCQ",
      new_code: "Not applicable for MCQ",
      time_complexity: "Not applicable for MCQ",
      space_complexity: "Not applicable for MCQ"
    }
  }

  // Process images for inclusion in prompt
  const imageContents = imageDataList.map((imageData) => ({
    type: "image_url",
    image_url: {
      url: `data:image/jpeg;base64,${imageData}`
    }
  }))

  // Build the prompt with error handling
  const problemStatement =
    problemInfo.problem_statement ?? "Problem statement not available"

  const inputFormatDescription =
    problemInfo.input_format?.description ??
    "Input format description not available"

  const inputParameters = problemInfo.input_format?.parameters
    ? problemInfo.input_format.parameters
        .map(
          (p) => `- ${p.name}: ${p.type}${p.subtype ? ` of ${p.subtype}` : ""}`
        )
        .join(" ")
    : "Input parameters not available"

  const outputFormatDescription =
    problemInfo.output_format?.description ??
    "Output format description not available"

  const returns = problemInfo.output_format?.type
    ? `Returns: ${problemInfo.output_format.type}${
        problemInfo.output_format.subtype
          ? ` of ${problemInfo.output_format.subtype}`
          : ""
      }`
    : "Returns: Output type not available"

  const constraints = problemInfo.constraints
    ? problemInfo.constraints
        .map((c) => {
          let constraintStr = `- ${c.description}`
          if (c.range) {
            constraintStr += ` (${c.parameter}: ${c.range.min} to ${c.range.max})`
          }
          return constraintStr
        })
        .join(" ")
    : "Constraints not available"

  let exampleTestCases = "Test cases not available"
  if (problemInfo.test_cases) {
    try {
      exampleTestCases = JSON.stringify(problemInfo.test_cases, null, 2)
    } catch {
      exampleTestCases = "Test cases not available"
    }
  }

  // Get language-specific instructions
  const detectedLanguage = problemInfo.detected_language || 'python'
  const languageInstructions = getLanguageSpecificInstructions(detectedLanguage)

  // Construct the debug prompt
  const debugPrompt = `
Given the following coding problem and its visual representation:

Problem Statement:
${problemStatement}

Detected Programming Language: ${detectedLanguage.toUpperCase()}
Language Confidence: ${problemInfo.language_confidence || 0.5}

${languageInstructions.contextPrompt}

Input Format:
${inputFormatDescription}
Parameters:
${inputParameters}

Output Format:
${outputFormatDescription}
${returns}

Constraints:
${constraints}

Example Test Cases:
${exampleTestCases}

First extract and analyze the code shown in the image. Then create an improved version while maintaining the same general approach and structure. The old code you save should ONLY be the exact code that you see on the screen, regardless of any optimizations or changes you make. Make all your changes in the new_code field. You should use the image that has the most recent, longest version of the code, making sure to combine multiple images if necessary.
Focus on keeping the solution syntactically similar but with optimizations and INLINE comments ONLY ON lines of code that were changed. Make sure there are no extra line breaks and all the code that is unchanged is in the same line as it was in the original code.

IMPORTANT FORMATTING NOTES:
1. Use actual line breaks (press enter for new lines) in both old_code and new_code
2. Maintain proper indentation with spaces or tabs as appropriate for ${detectedLanguage}
3. Add inline comments ONLY on changed lines in new_code using ${detectedLanguage} comment syntax
4. The entire response must be valid JSON that can be parsed
${languageInstructions.additionalRequirements}`

  // Construct the messages array
  const messages = [
    {
      role: "user",
      content: [
        {
          type: "text",
          text: debugPrompt
        },
        ...imageContents
      ]
    }
  ]

  // Define the function schema
  const functions = [
    {
      name: "provide_solution",
      description:
        "Debug based on the problem and provide a solution to the coding problem",
      parameters: {
        type: "object",
        properties: {
          thoughts: {
            type: "array",
            items: { type: "string" },
            description:
              "Share up to 3 key thoughts as you work through solving this problem for the first time. Write in the voice of someone actively reasoning through their approach, using natural pauses, uncertainty, and casual language that shows real-time problem solving. Each thought must be max 100 characters and be full sentences that don't sound choppy when read aloud.",
            maxItems: 3,
            thoughtGuidelines: [
              "First thought should capture that initial moment of recognition - connecting it to something familiar or identifying the core challenge. Include verbal cues like 'hmm' or 'this reminds me of' that show active thinking.",
              "Second thought must explore your emerging strategy and MUST explicitly name the algorithm or data structure being considered. Show both knowledge and uncertainty - like 'I could probably use a heap here, but I'm worried about...'",
              "Third thought should show satisfaction at having a direction while acknowledging you still need to work out specifics - like 'Okay, I think I see how this could work...'"
            ]
          },
          old_code: {
            type: "string",
            description:
              "The exact code implementation found in the image. There should be no additional lines of code added, this should only contain the code that is visible from the images, regardless of correctness or any fixes you can make. Include every line of code that are visible in the image.  You should use the image that has the most recent, longest version of the code, making sure to combine multiple images if necessary."
          },
          new_code: {
            type: "string",
            description:
              "The improved code implementation with in-line comments only on lines of code that were changed"
          },
          time_complexity: {
            type: "string",
            description:
              "Time complexity with explanation, format as 'O(_) because _.' Importantly, if there were slight optimizations in the complexity that don't affect the overall complexity, MENTION THEM."
          },
          space_complexity: {
            type: "string",
            description:
              "Space complexity with explanation, format as 'O(_) because _' Importantly, if there were slight optimizations in the complexity that don't affect the overall complexity, MENTION THEM."
          },
          language: {
            type: "string",
            enum: ["python", "javascript", "typescript", "java", "cpp", "c", "csharp", "sql", "bash", "shell", "go", "rust", "php", "ruby", "swift", "kotlin", "html", "css", "json", "yaml", "xml", "markdown", "text"],
            description: `The programming language used in the code (detected: ${detectedLanguage})`
          }
        },
        required: [
          "thoughts",
          "old_code",
          "new_code",
          "time_complexity",
          "space_complexity",
          "language"
        ]
      }
    }
  ]

  // Prepare the payload for the API call
  const payload = {
    model: "gpt-4o-mini",
    messages: messages,
    max_tokens: 4000,
    temperature: 0,
    functions: functions,
    function_call: { name: "provide_solution" }
  }

  try {
    // Send the request to the OpenAI API
    const storedApiKey = store.get("openaiApiKey") as string
    if (!storedApiKey) {
      throw new Error("OpenAI API key not set")
    }

    const response = await axios.post(
      "https://api.openai.com/v1/chat/completions",
      payload,
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${storedApiKey}`
        }
      }
    )

    // Extract the function call arguments from the response
    const functionCallArguments =
      response.data.choices[0].message.function_call.arguments

    // Parse and return the response
    return JSON.parse(functionCallArguments) as DebugSolutionResponse
  } catch (error: any) {
    if (error.response?.status === 404) {
      throw new Error(
        "API endpoint not found. Please check the model name and URL."
      )
    } else if (error.response?.status === 401) {
      throw new Error(
        "Please close this window and re-enter a valid Open AI API key."
      )
    } else if (error.response?.status === 429) {
      throw new Error(
        "API Key out of credits. Please refill your OpenAI API credits and try again."
      )
    } else {
      throw new Error(
        `OpenAI API error: ${
          error.response?.data?.error?.message || error.message
        }`
      )
    }
  }
}
